"use client";
import React, { useEffect, useRef } from "react";
import { createContext, ReactNode } from "react";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import { NETWORKS } from "@/utils/contants";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { setConnectedSocket } from "@/store/metadata.store";
import { useNetwork } from "./network";

type SocketContextType = Record<string, never>;

const SocketContext = createContext<SocketContextType>({});

enum SocketState {
  NotConnected = "NotConnected",
  Connected = "Connected",
  Disconnected = "Disconnected",
}

export const SocketProvider = ({
  children,
  accessToken,
}: {
  children: ReactNode;
  accessToken?: string;
}) => {
  const { currentNetwork } = useNetwork();
  const socketStateRef = useRef(SocketState.NotConnected);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    createSocketInstance(currentNetwork, accessToken);
    return () => {
      closeSocketInstance(currentNetwork);
    };
  }, [accessToken, currentNetwork]);

  useEffect(() => {
    const handleWhenSocketConnected = (event: TBroadcastEvent) => {
      dispatch(setConnectedSocket({ status: true }));
      socketStateRef.current = SocketState.Connected;
      AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA, {});
    };
    const handleWhenSocketDisconnected = (event: TBroadcastEvent) => {
      dispatch(setConnectedSocket({ status: false }));
      socketStateRef.current = SocketState.Disconnected;
    };
    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_CONNECTED,
      handleWhenSocketConnected
    );
    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_DISCONNECTED,
      handleWhenSocketDisconnected
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_CONNECTED,
        handleWhenSocketConnected
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_DISCONNECTED,
        handleWhenSocketDisconnected
      );
    };
  }, [connectedSocket]);

  return <SocketContext.Provider value={{}}>{children}</SocketContext.Provider>;
};
