import { useCallback } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { TPair } from "@/types";
import DexesFactory from "../utils/simulates/dexes/DexesFactory";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import {
  getCoinBalanceOnchain,
  getReferenceGasPrice,
} from "../utils/suiClient";
import { convertDecToMist } from "../utils/helper";
import {
  SUI_DECIMALS,
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
} from "../utils/contants";
import useProcessTx from "./useProcessTx";
import { toastError } from "../libs/toast";
import { EDex } from "../enums/dex.enum";
import BigNumber from "bignumber.js";
import config from "@/config";

export const useExternalWallet = () => {
  const currentAccount = useCurrentAccount();
  const { processTx } = useProcessTx();

  const onBuyToken = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair?.dex.dex as any,
          objectId: pair?.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        let exactAmountIn = convertDecToMist(buyAmount, SUI_DECIMALS);
        let output: { tx: Transaction; amountOut: string | number } | null =
          null;

        const raidenxFee = BigNumber(exactAmountIn)
          .times(config.raidenxFeeRate)
          .toFixed();

        const isSuiQuoteToken =
          pair?.tokenQuote.address === SUI_TOKEN_ADDRESS_SHORT ||
          pair?.tokenQuote.address === SUI_TOKEN_ADDRESS_FULL;
        if (isSuiQuoteToken) {
          const totalUserBalance = await getCoinBalanceOnchain(
            currentAccount.address,
            SUI_TOKEN_ADDRESS_FULL
          );
          const amountWithFee = BigNumber(exactAmountIn)
            .plus(BigNumber(raidenxFee))
            .toFixed(BigNumber.ROUND_CEIL);
          if (BigNumber(totalUserBalance).lt(BigNumber(amountWithFee))) {
            exactAmountIn = BigNumber(totalUserBalance)
              .minus(BigNumber(raidenxFee))
              .toFixed(BigNumber.ROUND_FLOOR);
          }
        }

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          output = await instance.buildBuyTransaction(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenBase,
            pair?.tokenQuote,
            pool?.objectId,
            gasBasePrice
          );
        } else if (pool.dex === EDex.SUIAIFUN) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice,
            true // isBuyBySuiToken
          );
        } else if (pool.dex === EDex.STEAM) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pair.isXQuoteToken,
            pair.feeTier,
            pool.objectId,
            gasBasePrice
          );
        } else {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice
          );
        }

        console.log(output, "output ExternalWallet");

        if (!output?.tx) {
          throw new Error("Failed to build transaction");
        }

        if (instance.name !== "CetusAggregatorSimulate") {
          const raidenxFeeCoin = coinWithBalance({
            balance: BigInt(raidenxFee.toString()),
            type: pair?.tokenQuote.address,
          });
          output.tx.transferObjects(
            [raidenxFeeCoin],
            output.tx.pure.address(config.raidenxFeeReceiverAddress)
          );
        }

        await processTx(output.tx, onSuccess);
      } catch (error: any) {
        console.log(error, "onBuyToken error");
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    [currentAccount?.address, processTx]
  );

  const onSellToken = useCallback(
    async (
      pair: TPair,
      sellPercent: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        let output: { tx: Transaction; amountOut: string | number } | null =
          null;

        const totalUserBalance = await getCoinBalanceOnchain(
          currentAccount.address,
          pair?.tokenBase.address
        );

        const sellAmount = BigNumber(totalUserBalance)
          .multipliedBy(BigNumber(sellPercent))
          .dividedBy(100)
          .integerValue(BigNumber.ROUND_FLOOR)
          .toFixed();
        const raidenxFee = BigNumber(sellAmount)
          .times(config.raidenxFeeRate)
          .integerValue(BigNumber.ROUND_FLOOR)
          .toFixed();

        const sellPercentFeeDeducted = BigNumber(sellAmount)
          .minus(raidenxFee)
          .dividedBy(totalUserBalance)
          .multipliedBy(100)
          .toFixed(4);

        output = await instance.extractQuoteTokenOut(
          currentAccount.address,
          pair?.tokenBase,
          pair?.tokenQuote,
          sellPercentFeeDeducted,
          pool.objectId,
          gasBasePrice
        );

        if (!output?.tx) {
          throw new Error("Failed to build transaction");
        }

        if (instance.name !== "CetusAggregatorSimulate") {
          const raidenxFeeCoin = coinWithBalance({
            balance: BigInt(raidenxFee.toString()),
            type: pair?.tokenBase.address,
          });

          output.tx.transferObjects(
            [raidenxFeeCoin],
            output.tx.pure.address(config.raidenxFeeReceiverAddress)
          );
        }

        const result = await processTx(output.tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    [currentAccount?.address, processTx]
  );

  return {
    onBuyToken,
    onSellToken,
  };
};
