"use client";

import { ReactNode } from "react";
import "@mysten/dapp-kit/dist/index.css";
import { getFullnodeUrl } from "@mysten/sui/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useNetwork } from "@/context/network";
import { NETWORKS } from "@/utils/contants";
import { HypeWalletProvider } from "./HypeWalletProvider";
import { SomniaWalletProvider } from "./SomniaWalletProvider";

const queryClient = new QueryClient();

const SuiWalletProvider = ({ children }: { children: ReactNode }) => {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

interface MultiNetworkWalletProviderProps {
  children: ReactNode;
}

export const MultiNetworkWalletProvider = ({
  children,
}: MultiNetworkWalletProviderProps) => {
  const { currentNetwork } = useNetwork();

  switch (currentNetwork) {
    case NETWORKS.SUI:
      return <SuiWalletProvider>{children}</SuiWalletProvider>;

    case NETWORKS.HYPEREVM:
      return <HypeWalletProvider>{children}</HypeWalletProvider>;

    case NETWORKS.SOMNIA:
      return <SomniaWalletProvider>{children}</SomniaWalletProvider>;

    default:
      return <SuiWalletProvider>{children}</SuiWalletProvider>;
  }
};
