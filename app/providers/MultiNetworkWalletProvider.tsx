"use client";

import { ReactNode } from "react";
import { useNetwork } from "@/context/network";
import { NETWORKS } from "@/utils/contants";
import { HypeWalletProvider } from "./HypeWalletProvider";
import { SomniaWalletProvider } from "./SomniaWalletProvider";
import SuiWalletProvider from "../WalletProvider";

interface MultiNetworkWalletProviderProps {
  children: ReactNode;
}

export const MultiNetworkWalletProvider = ({
  children,
}: MultiNetworkWalletProviderProps) => {
  const { currentNetwork } = useNetwork();

  switch (currentNetwork) {
    case NETWORKS.SUI:
      return <SuiWalletProvider>{children}</SuiWalletProvider>;

    case NETWORKS.HYPEREVM:
      return <HypeWalletProvider>{children}</HypeWalletProvider>;

    case NETWORKS.SOMNIA:
      return <SomniaWalletProvider>{children}</SomniaWalletProvider>;

    default:
      return <SuiWalletProvider>{children}</SuiWalletProvider>;
  }
};
