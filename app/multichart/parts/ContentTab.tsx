"use client";

import React, { useEffect, useMemo, useState } from "react";
import { PlusIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { ModalSearch } from "@/modals";
import { ITabMultiChart } from "../page";
import Storage from "@/libs/storage";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import _ from "lodash";
import { compareBN } from "@/utils/helper";
import { MIN_SUI_LIQUIDITY_TO_SIMULATE } from "@/utils/simulates";
import { normalizeStructTag } from "@mysten/sui/utils";
import { RootPairProvider } from "../../[network]/(token_detail)/provider";
import { PairChartDetails } from "./PairChartDetails";
import { NETWORKS } from "@/utils/contants";
import rf from "@/services/RequestFactory";
import { TPair } from "@/types";
import { isDexHasBondingCurve } from "@/utils/dex";
import { useNetwork } from "@/context";
const AddChart = ({
  tabs,
  setTabs,
  height,
  dataTabActive,
}: {
  tabs: ITabMultiChart[];
  dataTabActive?: ITabMultiChart;
  height: number;
  setTabs: (value: ITabMultiChart[]) => void;
}) => {
  const [isShowModalSearch, setIsShowModalSearch] = useState<boolean>(false);

  const onAddChart = (pairSlug: string) => {
    if (dataTabActive?.pairs.includes(pairSlug)) return;
    const newTabs = tabs.map((item) => {
      if (dataTabActive?.id === item.id && item.pairs.length < 10) {
        return {
          ...item,
          pairs: [...item.pairs, pairSlug],
        };
      }
      return item;
    });
    Storage.setDataMultiChart(newTabs);
    setTabs(newTabs);
  };

  return (
    <div
      style={{
        height: `${height}px`,
      }}
      className="border-white-50 flex flex-col items-center justify-center rounded-[10px] border"
    >
      <div>
        <AppButton variant="buy" onClick={() => setIsShowModalSearch(true)}>
          <PlusIcon />
        </AppButton>
      </div>
      <div className="heading-sm-semibold-16 text-brand-600 mb-1 mt-5">
        Add Chart
      </div>
      <div className="body-md-regular-14 text-white-700">
        {10 - (dataTabActive?.pairs?.length || 0)} of 10 slots remaining
      </div>

      {isShowModalSearch && (
        <ModalSearch
          onSelectPair={onAddChart}
          isOpen={isShowModalSearch}
          onClose={() => setIsShowModalSearch(false)}
        />
      )}
    </div>
  );
};

const PairChart = ({
  pair,
  height,
  isPosition,
  onRemoveChart,
}: {
  pair: TPair;
  height: number;
  isPosition?: boolean;
  onRemoveChart?: (pairSlug: string) => void;
}) => {
  const isBondingPair = pair;
  return (
    <RootPairProvider externalPair={pair} enableGraduatedPairChecker={false}>
      <div
        style={{
          height: `${height}px`,
        }}
        className="border-white-50 flex flex-col items-center justify-center overflow-hidden rounded-[10px] border"
      >
        <PairChartDetails
          isPosition={isPosition}
          onRemoveChart={onRemoveChart}
        />
      </div>
    </RootPairProvider>
  );
};

export const ContentTab = ({
  height,
  tabs,
  setTabs,
  tabActive,
}: {
  height: number;
  tabActive: string;
  tabs: ITabMultiChart[];
  setTabs: (value: ITabMultiChart[]) => void;
}) => {
  const dataTabActive = useMemo(() => {
    return tabs.find((item) => item.id === tabActive);
  }, [tabActive, tabs]);
  const [listPair, setListPair] = useState<TPair[]>([]);
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    setListPair([]);
  }, [tabActive]);

  const onRemoveChart = (pairSlug: string) => {
    const newTabs = tabs.map((item) => {
      if (dataTabActive?.id === item.id) {
        const newPairs = item.pairs.filter((item) => item !== pairSlug);
        return {
          ...item,
          pairs: [...newPairs],
        };
      }
      return item;
    });
    Storage.setDataMultiChart(newTabs);
    setTabs(newTabs);
    setListPair((prev) => prev.filter((pair) => pair.slug !== pairSlug));
  };

  useEffect(() => {
    if (!dataTabActive?.pairs?.length) {
      return;
    }
    const uniquePairSlugs = _.uniq(dataTabActive?.pairs || []);
    const fetchPairs = async () => {
      for (const item of uniquePairSlugs) {
        // Check if pair already exists in listPair
        const existingPair = listPair.find((p) => p.slug === item);
        if (!existingPair) {
          let pair = await rf
            .getRequest("PairRequest")
            .getPair(currentNetwork, item);
          if (isDexHasBondingCurve(pair?.dex?.dex) && pair.graduatedSlug) {
            console.log("pair.graduatedSlug", pair.graduatedSlug);
            pair = await rf
              .getRequest("PairRequest")
              .getPair(currentNetwork, pair.graduatedSlug);
          }
          setListPair((prev) => _.uniqBy([...prev, pair], "slug"));
        }
      }
    };
    fetchPairs();
  }, [dataTabActive?.id, dataTabActive?.pairs?.length, listPair?.length]);

  return (
    <>
      {listPair?.map((pair, index) => {
        return (
          <PairChart
            key={`${pair.slug}-${index}`}
            pair={pair}
            height={height}
            onRemoveChart={onRemoveChart}
          />
        );
      })}

      {dataTabActive && dataTabActive?.pairs?.length < 10 && (
        <AddChart
          height={height}
          tabs={tabs}
          setTabs={setTabs}
          dataTabActive={dataTabActive}
        />
      )}
    </>
  );
};

export const ContentTabPosition = ({ height }: { height: number }) => {
  const positions = useSelector((state: RootState) => state.user.positions);
  const [listPair, setListPair] = useState<TPair[]>([]);
  const { currentNetwork } = useNetwork();

  const dataPosition = useMemo(() => {
    let data = positions.filter(
      (item) =>
        compareBN(item.liquidity || 0, MIN_SUI_LIQUIDITY_TO_SIMULATE) >= 0
    );
    data = _.sortBy(data, "timestamp").reverse();
    data = _.uniqWith(
      data,
      (a, b) =>
        normalizeStructTag(a.token.address) ===
        normalizeStructTag(b.token.address)
    );
    return data.slice(0, 10);
  }, [positions]);

  useEffect(() => {
    if (!dataPosition?.length) {
      setListPair([]);
      return;
    }
    const uniquePairs = _.uniqBy(dataPosition, "pair");
    const fetchPairs = async () => {
      for (const item of uniquePairs) {
        // Check if pair already exists in listPair
        const existingPair = listPair.find((p) => p.pairId === item.pair);
        if (!existingPair) {
          let pair = await rf
            .getRequest("PairRequest")
            .getPair(currentNetwork, item.pair);
          if (isDexHasBondingCurve(pair?.dex?.dex) && pair.graduatedSlug) {
            console.log("pair.graduatedSlug", pair.graduatedSlug);
            pair = await rf
              .getRequest("PairRequest")
              .getPair(currentNetwork, pair.graduatedSlug);
          }
          setListPair((prev) => _.uniqBy([...prev, pair], "slug"));
        }
      }
    };
    fetchPairs();
  }, [dataPosition?.length, listPair?.length]);

  return (
    <>
      {listPair?.map((pair, index) => {
        return (
          <PairChart
            key={`${pair.slug}-${index}`}
            pair={pair}
            height={height}
            isPosition
            onRemoveChart={() => {}}
          />
        );
      })}
    </>
  );
};
